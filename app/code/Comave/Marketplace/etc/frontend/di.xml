<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="\Magento\Framework\App\Action\Action">
        <plugin name="Comave_Marketplace::controllerPathValidator"
                type="Comave\Marketplace\Model\Plugin\Action\ControllerPathValidator"
                sortOrder="10" />
    </type>

    <type name="Webkul\Marketplace\Ui\DataProvider\ProductListDataProvider">
        <plugin name="comave_marketplace_add_ean_attribute"
                type="Comave\Marketplace\Plugin\ProductListDataProvider"
                sortOrder="10" />
    </type>
</config>
