<?php

declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Webkul\Marketplace\Ui\DataProvider\ProductListDataProvider;

class ProductListDataProvider
{
    private const string EAN_ATTRIBUTE_CODE = 'ean';
    private const string SELLER_EDITABLE_ATTRIBUTE = 'is_seller_editable';

    public function __construct(
        private readonly EavConfig $eavConfig
    ) {}

    /**
     * Add EAN attribute to the collection after construction
     *
     * @param ProductListDataProvider $subject
     * @return void
     */
    public function after__construct(ProductListDataProvider $subject): void
    {
        // Check if EAN attribute exists and is seller editable
        if ($this->isEanAttributeSellerEditable()) {
            $collection = $subject->getCollection();
            if ($collection instanceof Collection) {
                $collection->addAttributeToSelect(self::EAN_ATTRIBUTE_CODE);
            }
        }
    }

    /**
     * Check if EAN attribute exists and is seller editable
     *
     * @return bool
     */
    private function isEanAttributeSellerEditable(): bool
    {
        try {
            $attribute = $this->eavConfig->getAttribute(
                ProductAttributeInterface::ENTITY_TYPE_CODE,
                self::EAN_ATTRIBUTE_CODE
            );

            if (!$attribute || !$attribute->getId()) {
                return false;
            }

            // Check if the attribute has the seller editable flag
            return (bool) $attribute->getData(self::SELLER_EDITABLE_ATTRIBUTE);
        } catch (\Exception $e) {
            return false;
        }
    }
}
