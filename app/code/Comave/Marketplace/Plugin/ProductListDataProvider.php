<?php

declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Webkul\Marketplace\Ui\DataProvider\ProductListDataProvider;

class ProductListDataProvider
{
    private const string EAN_ATTRIBUTE_CODE = 'ean';

    /**
     * Add EAN attribute to the collection after construction
     *
     * @param ProductListDataProvider $subject
     * @return void
     */
    public function after__construct(ProductListDataProvider $subject): void
    {
        $collection = $subject->getCollection();
        if ($collection instanceof Collection) {
            // Simply add EAN attribute to the collection
            // No need to check seller_editable since we want to show it regardless
            $collection->addAttributeToSelect(self::EAN_ATTRIBUTE_CODE);
        }
    }
}
