<?php

declare(strict_types=1);

namespace Comave\Marketplace\Console\Command;

use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Eav\Model\Config as EavConfig;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TestEanAttribute extends Command
{
    private const string EAN_ATTRIBUTE_CODE = 'ean';
    private const string SELLER_EDITABLE_ATTRIBUTE = 'is_seller_editable';

    public function __construct(
        private readonly EavConfig $eavConfig
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('comave:test:ean-attribute')
            ->setDescription('Test EAN attribute configuration for seller editability');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $attribute = $this->eavConfig->getAttribute(
                ProductAttributeInterface::ENTITY_TYPE_CODE,
                self::EAN_ATTRIBUTE_CODE
            );

            if (!$attribute || !$attribute->getId()) {
                $output->writeln('<error>EAN attribute not found</error>');
                return Command::FAILURE;
            }

            $output->writeln('<info>EAN Attribute Information:</info>');
            $output->writeln('Attribute ID: ' . $attribute->getId());
            $output->writeln('Attribute Code: ' . $attribute->getAttributeCode());
            $output->writeln('Label: ' . $attribute->getFrontendLabel());
            $output->writeln('Is Seller Editable: ' . ($attribute->getData(self::SELLER_EDITABLE_ATTRIBUTE) ? 'Yes' : 'No'));
            $output->writeln('Is Used in Grid: ' . ($attribute->getIsUsedInGrid() ? 'Yes' : 'No'));
            $output->writeln('Is Visible in Grid: ' . ($attribute->getIsVisibleInGrid() ? 'Yes' : 'No'));
            $output->writeln('Is Filterable in Grid: ' . ($attribute->getIsFilterableInGrid() ? 'Yes' : 'No'));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln('<error>Error: ' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }
    }
}
