<?php

declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class UpdateEanAttributeSellerEditable implements DataPatchInterface
{
    private const string EAN_ATTRIBUTE_CODE = 'ean';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    /**
     * @inheritdoc
     */
    public function apply(): self
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        // Check if EAN attribute exists
        $attributeId = $eavSetup->getAttributeId(Product::ENTITY, self::EAN_ATTRIBUTE_CODE);
        
        if ($attributeId) {
            // Update the EAN attribute to be seller editable
            $eavSetup->updateAttribute(
                Product::ENTITY,
                self::EAN_ATTRIBUTE_CODE,
                'is_seller_editable',
                1
            );
        }

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [
            \Comave\BigBuy\Setup\Patch\Data\InstallAttributes::class,
        ];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
